import { LinkIcon } from "@everstage/evericons/duocolor";
import {
  InfoCircleIcon,
  PinIcon,
  ClockIcon,
} from "@everstage/evericons/duotone";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import React, { useContext, useEffect, useState, useCallback } from "react";
import { useMutation } from "react-query";

import { RBAC_ROLES } from "~/Enums";
import { useUserPermissionStore } from "~/GlobalStores/UserPermissionStore";
import {
  ClickBoundary,
  EverButton,
  EverDivider,
  EverFormatter,
  EverInput,
  EverLoader,
  EverTg,
  EverTooltip,
  message,
} from "~/v2/components";

import { DatasheetContext } from "../../DatasheetStore";
import useFetchApiWithAuth from "../../useFetchApiWithAuth";
import { DATASHEET_GRAPH_LEVEL, handleOpenDatasheetGraph } from "../../utils";

/**
 * Component for displaying/editing the name of a datasheet and action button along the datasheet name.
 *
 * @param {Object} props - The props object containing the following properties:
 * @param {Function} props.t - Translation function
 * @returns {JSX.Element} React component
 */

export const DatasheetName = observer(
  ({ t, isTimelineModalVisible, setTimelineModalVisible }) => {
    const datasheetStore = useContext(DatasheetContext);

    const [isEditing, setIsEditing] = useState(false);
    const [newDatasheetName, setNewDatasheetName] = useState(
      datasheetStore.currentDatasheetName
    );
    const { hasPermissions } = useUserPermissionStore();

    const { fetchData } = useFetchApiWithAuth();

    const renameDataSheet = useMutation(
      // Mutation function that sends a PATCH request to rename the datasheet
      (payload) =>
        fetchData(`/datasheet_ninja/`, "PATCH", {
          datasheet_id: datasheetStore?.datasheetId,
          name: payload.name,
        }),

      {
        onError: async (error) => {
          setNewDatasheetName(datasheetStore.currentDatasheetName); // Revert the changes made in edit datasheet name input
          setIsEditing(false); // Disable editing mode on error
          let errorMessage = error?.toString();
          if (errorMessage?.includes("Rename already in progress")) {
            return;
          }
          message.error(errorMessage); // Display error message to the user
        },
        onSuccess: async (data) => {
          // Retrieve recent datasheets from localStorage or initialize as an empty array if not available
          const jsonRecentDatasheets =
            localStorage.getItem("recent_datasheets") || "[]";
          // Parse recent datasheets into an array
          const parsedRecentDatasheets = JSON.parse(jsonRecentDatasheets);
          // Initialize a flag to track if any recent datasheet has been renamed
          let isAnyRecentDatasheetRenamed = false;
          // Iterate over each datasheet in the parsed array
          parsedRecentDatasheets.forEach((sheet) => {
            // Check if the current datasheet matches the one being renamed
            if (sheet?.datasheet_id === datasheetStore.datasheetId) {
              // Update the name of the matched datasheet
              sheet.name = newDatasheetName;
              // Set the flag to true indicating a datasheet has been renamed
              isAnyRecentDatasheetRenamed = true;
            }
          });
          // Save the updated array of recent datasheets back to local storage
          localStorage.setItem(
            "recent_datasheets",
            JSON.stringify(parsedRecentDatasheets)
          );
          // If any datasheet has been renamed, update the recent sheets in the store
          if (isAnyRecentDatasheetRenamed) {
            datasheetStore.setRecentSheets(parsedRecentDatasheets);
          }
          // Check if the current datasheet is in the pinned datasheet list
          if (
            !datasheetStore.pinnedDatasheetList.every(
              (x) => x.datasheet_id !== datasheetStore.datasheetId
            )
          ) {
            // If the datasheet is pinned, refetch the pinned datasheets
            await datasheetStore.pinnedRefetchFn();
          }
          setIsEditing(false); // Disable editing mode on success
          if (data?.name) {
            // Check if the response contains the new name
            datasheetStore.setCurrentDatasheetName(data.name); // Update the store with the new name
          }
          datasheetStore.viewByRefetchFn();
          datasheetStore.globalDatabookRefetch();
          message.success("Datasheet successfully renamed"); // Display success message to the user
        },
      }
    );

    const handleRenameDatasheet = useCallback(
      debounce(async (name) => {
        await renameDataSheet.mutate({
          name: name,
        });
      }, 300), // Debounce time in milliseconds
      [renameDataSheet.mutate]
    );

    function handleChangeDatasheetName(setEdit, changeName) {
      if (setEdit) {
        setIsEditing(true);
      } else {
        if (
          changeName &&
          newDatasheetName?.trim() != "" &&
          newDatasheetName !== datasheetStore.currentDatasheetName
        ) {
          handleRenameDatasheet(newDatasheetName);
        } else {
          setNewDatasheetName(datasheetStore.currentDatasheetName);
        }
        setIsEditing(false);
      }
    }

    const pinRequest = useMutation(
      () => {
        return fetchData(
          `/datasheets/${datasheetStore.datasheetId}/pin`,
          "POST"
        );
      },
      {
        onError: (error) => {
          message.error(error?.toString());
        },
        onSuccess: async () => {
          await datasheetStore.pinnedRefetchFn();
        },
      }
    );

    const handlePin = useCallback(
      debounce(async () => {
        pinRequest.mutate();
      }, 200), // Debounce time in milliseconds
      [pinRequest.mutate]
    );

    function handleEscape({ key }) {
      if (key === "Escape") handleChangeDatasheetName(false, false);
    }

    useEffect(() => {
      setNewDatasheetName(datasheetStore.currentDatasheetName);
    }, [datasheetStore.currentDatasheetName]);

    const handleDetailsDatasheetView = () => {
      const url = new URL(window.location);
      const searchParams = new URLSearchParams(url.search);
      // Set or update the query parameter with the provided key and value
      searchParams.set("detailsDatasheetId", datasheetStore.datasheetId);
      url.search = searchParams.toString();
      // Use history.replaceState to update the URL without reloading the page
      window.history.replaceState({}, "", url);
      datasheetStore.setDetailsDatasheetId(datasheetStore.datasheetId);
      datasheetStore.setDrawerVisibility(true);
    };

    const dependentSheetCount =
      datasheetStore.datasheetDetails?.downstream_sheet_details?.length || 0;

    return (
      <div className="flex flex-col gap-0.5 w-full">
        <div className="flex gap-2 items-center w-1/2">
          {datasheetStore.datasheetDetailsIsLoading ? (
            <EverLoader.Skeleton
              config={[1]}
              className="h-[27px] !p-0 !pl-1 !w-56"
            />
          ) : isEditing ? (
            <ClickBoundary
              className="w-full"
              onClickOutside={() => handleChangeDatasheetName(false, false)}
            >
              <EverInput
                value={newDatasheetName}
                onChange={(e) => setNewDatasheetName(e.target.value)}
                onKeyDown={(e) => handleEscape(e)}
                onPressEnter={() => {
                  if (renameDataSheet.isLoading) {
                    return;
                  }
                  handleChangeDatasheetName(false, true);
                }}
                size="small"
                className="min-w-48 max-w-[580px]"
                style={{ width: `${newDatasheetName.length}ch` }}
                autoFocus
              />
            </ClickBoundary>
          ) : (
            <>
              <EverTooltip title={datasheetStore.currentDatasheetName}>
                <div
                  className="hover:bg-ever-base-100 px-2 py-1 rounded border border-solid border-transparent hover:border-ever-base-400 max-w-52 truncate"
                  onClick={() => handleChangeDatasheetName(true)}
                >
                  <EverTg.Heading3>
                    {datasheetStore.currentDatasheetName}
                  </EverTg.Heading3>
                </div>
              </EverTooltip>

              <div className="flex gap-1">
                <EverTooltip title="Sheet details">
                  <EverButton.Icon
                    color="base"
                    type="ghost"
                    icon={<InfoCircleIcon className="!w-4 !h-4" />}
                    size="small"
                    className="!w-6 !h-6"
                    onClick={handleDetailsDatasheetView}
                  />
                </EverTooltip>
                <EverTooltip title="Copy link">
                  <EverButton.Icon
                    color="base"
                    type="ghost"
                    icon={<LinkIcon className="!w-4 !h-4" />}
                    size="small"
                    onClick={async () => {
                      const sheetUrl = window?.location?.href || "";
                      try {
                        await navigator.clipboard.writeText(sheetUrl);
                        message.success("Copied to clipboard");
                      } catch (error) {
                        console.error(
                          "Failed to copy link to clipboard:",
                          error
                        );
                      }
                    }}
                    className="!w-6 !h-6"
                  />
                </EverTooltip>
                {hasPermissions(RBAC_ROLES.MANAGE_DATABOOK) && (
                  <EverTooltip title="Pin sheet">
                    <EverButton.Icon
                      color="base"
                      type="ghost"
                      icon={<PinIcon className="!w-4 !h-4" />}
                      size="small"
                      onClick={handlePin}
                      className="!w-6 !h-6"
                    />
                  </EverTooltip>
                )}
                <EverTooltip title="View Timeline">
                  <EverButton.Icon
                    color="base"
                    type="ghost"
                    icon={<ClockIcon className="!w-4 !h-4" />}
                    size="small"
                    onClick={() => setTimelineModalVisible(true)}
                    className="!w-6 !h-6"
                  />
                </EverTooltip>
              </div>
            </>
          )}
        </div>
        {datasheetStore.datasheetDetailsIsLoading ? (
          <EverLoader.Skeleton
            config={[1]}
            className="h-[27px] !p-0 !pl-1 !w-[500px]"
          />
        ) : (
          <div className="flex gap-2 pl-3 items-center">
            {dependentSheetCount > 0 ? (
              <>
                <EverButton
                  type="link"
                  color="primary"
                  className="!p-0 !h-5"
                  size="small"
                  onClick={() =>
                    handleOpenDatasheetGraph(
                      datasheetStore,
                      datasheetStore.datasheetId,
                      DATASHEET_GRAPH_LEVEL.SHEET,
                      datasheetStore.datasheetDetails?.name,
                      datasheetStore.datasheetDetails?.databook_id
                    )
                  }
                  disabled={!hasPermissions(RBAC_ROLES.MANAGE_DATABOOK)}
                >
                  <EverTg.Caption>
                    {dependentSheetCount}{" "}
                    {dependentSheetCount === 1
                      ? "dependent sheet"
                      : "dependent sheets"}
                  </EverTg.Caption>
                </EverButton>
                <EverDivider type="vertical" />
              </>
            ) : (
              <></>
            )}
            {datasheetStore.datasheetDetails?.total_connected_commission_plans >
            0 ? (
              <>
                <EverButton
                  type="link"
                  color="primary"
                  className="!p-0 !h-5"
                  size="small"
                  onClick={handleDetailsDatasheetView}
                >
                  <EverTg.Caption>
                    {
                      datasheetStore.datasheetDetails
                        ?.total_connected_commission_plans
                    }{" "}
                    {datasheetStore.datasheetDetails
                      ?.total_connected_commission_plans === 1
                      ? `${t("COMMISSION").toLowerCase()} plan`
                      : `${t("COMMISSION").toLowerCase()} plans`}
                  </EverTg.Caption>
                </EverButton>
                <EverDivider type="vertical" />
              </>
            ) : (
              <></>
            )}
            <>
              <EverTg.Caption className="text-ever-base-content-mid">
                Last refreshed on{" "}
                <EverTg.Caption className="text-ever-base-content-mid">
                  {datasheetStore.datasheetDetailsIsLoading ? (
                    "-"
                  ) : datasheetStore.datasheetDetails?.last_generated_at ? (
                    <EverFormatter.DateTime
                      className="font-normal text-xs text-ever-base-content-mid"
                      date={datasheetStore?.datasheetDetails?.last_generated_at}
                      type="short"
                      targetTimeZone="local"
                    />
                  ) : (
                    "-"
                  )}
                </EverTg.Caption>
              </EverTg.Caption>
            </>
          </div>
        )}
      </div>
    );
  }
);
