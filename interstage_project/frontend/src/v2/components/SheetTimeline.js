import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import React, { useEffect, useState } from "react";

import { useAuthStore } from "~/GlobalStores/AuthStore";

import { EverGroupAvatar } from "./EverGroupAvatar";
import { EverList } from "./EverList";
import { EverListItem } from "./EverListItem";
import { EverTooltip } from "./EverTooltip";
import { EverTg } from "./EverTypography";

dayjs.extend(relativeTime);

const actionTypeLabels = {
  sheet_created: "Sheet Created",
  sheet_archived: "Sheet Archived",
  sheet_generated: "Sheet Generated",
  transformation_added: "Transformation Added",
  transformation_edited: "Transformation Edited",
  variable_added: "Variable Added",
  variable_edited: "Variable Edited",
  variable_selected: "Variable Selected",
  variable_deselected: "Variable Deselected",
  adjustment_added: "Adjustment Added",
  permission_changed: "Permission Changed",
  // ...add more as needed
};

const SheetTimeline = ({ datasheetId, databookId }) => {
  const [timeline, setTimeline] = useState([]);
  const [loading, setLoading] = useState(true);
  const { accessToken } = useAuthStore();

  useEffect(() => {
    if (!datasheetId || !databookId || !accessToken) {
      setLoading(false);
      return;
    }

    setLoading(true);
    fetch(`/datasheets/${datasheetId}/timeline/${databookId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then((res) => res.json())
      .then((data) => setTimeline(data.timeline || []))
      .catch((error) => {
        console.error("Error fetching timeline:", error);
        setTimeline([]);
      })
      .finally(() => setLoading(false));
  }, [datasheetId, databookId, accessToken]);

  if (loading) return <div>Loading...</div>;

  if (timeline.length === 0) {
    return <div>No timeline data available.</div>;
  }

  return (
    <EverList enableScrollShadow={false}>
      {timeline.map((item) => (
        <EverListItem
          key={item.timestamp}
          prepend={
            <EverGroupAvatar
              avatars={[
                {
                  name: item.performed_by || "Unknown",
                  image: null,
                },
              ]}
              size="small"
            />
          }
          title={
            <div>
              <EverTg.Heading4 className="">
                {actionTypeLabels[item.action_type] || item.action_type}
              </EverTg.Heading4>
              <EverTg.Text className="">{item.performed_by}</EverTg.Text>
              <EverTooltip
                title={dayjs(item.timestamp).format("YYYY-MM-DD HH:mm")}
                mode="light"
              >
                <span>
                  <EverTg.Caption className="">
                    {dayjs(item.timestamp).fromNow()}
                  </EverTg.Caption>
                </span>
              </EverTooltip>
              <div>
                {Object.entries(item.details || {}).map(([k, v]) => (
                  <div key={k}>
                    <b>{k}:</b>{" "}
                    {typeof v === "object" ? JSON.stringify(v) : String(v)}
                  </div>
                ))}
              </div>
            </div>
          }
        />
      ))}
    </EverList>
  );
};

export default SheetTimeline;
