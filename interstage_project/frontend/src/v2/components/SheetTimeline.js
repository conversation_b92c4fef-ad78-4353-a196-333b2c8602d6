import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useEffect, useState } from "react";
// Simple icon components as fallbacks
const ChevronDownIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
);

const ChevronUpIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 15l7-7 7 7"
    />
  </svg>
);

const MagnifyingGlassIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const ArrowPathIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
    />
  </svg>
);

const EyeSlashIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
    />
  </svg>
);

const AdjustmentsHorizontalIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
    />
  </svg>
);

const PlusIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 4v16m8-8H4"
    />
  </svg>
);

const PencilIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
    />
  </svg>
);

const CalculatorIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
    />
  </svg>
);

const CogIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
  </svg>
);

const ShieldCheckIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
    />
  </svg>
);

import { useAuthStore } from "~/GlobalStores/AuthStore";

import { EverButton } from "./ever-button/EverButton";
import { EverCheckbox } from "./ever-checkbox/EverCheckbox";
import { EverGroupAvatar } from "./EverGroupAvatar";
import { EverInput } from "./EverInput";
import { EverTg } from "./EverTypography";

dayjs.extend(relativeTime);

// Action type configurations with icons and labels
const actionTypeConfig = {
  sheet_created: {
    label: "Created Sheet",
    icon: PlusIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    tag: "add",
  },
  sheet_deleted: {
    label: "Deleted Sheet",
    icon: EyeSlashIcon,
    color: "text-red-600",
    bgColor: "bg-red-50",
    tag: "delete",
  },
  sheet_generated: {
    label: "Generated Sheet",
    icon: CogIcon,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    tag: "generate",
  },
  transformation_added: {
    label: "Added Data Transformation",
    icon: AdjustmentsHorizontalIcon,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    tag: "add",
  },
  transformation_edited: {
    label: "Updated Data Transformation",
    icon: PencilIcon,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    tag: "edit",
  },
  variable_added: {
    label: "Added Variable",
    icon: PlusIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    tag: "add",
  },
  variable_edited: {
    label: "Updated Variable",
    icon: PencilIcon,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    tag: "edit",
  },
  variable_selected: {
    label: "Selected Variable",
    icon: ShieldCheckIcon,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    tag: "select",
  },
  variable_deselected: {
    label: "Deselected Variable",
    icon: EyeSlashIcon,
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    tag: "deselect",
  },
  adjustment_added: {
    label: "Added Adjustment",
    icon: CalculatorIcon,
    color: "text-indigo-600",
    bgColor: "bg-indigo-50",
    tag: "add",
  },
  permission_changed: {
    label: "Changed Permission",
    icon: ShieldCheckIcon,
    color: "text-yellow-600",
    bgColor: "bg-yellow-50",
    tag: "edit",
  },
};

const SheetTimeline = ({ datasheetId, databookId }) => {
  const [timeline, setTimeline] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedActionTypes, setSelectedActionTypes] = useState(
    new Set(Object.keys(actionTypeConfig))
  );
  const [selectedUsers, setSelectedUsers] = useState(new Set());
  const [showFilters, setShowFilters] = useState(true);
  const [expandedItems, setExpandedItems] = useState(new Set());
  const { accessToken } = useAuthStore();

  useEffect(() => {
    if (!datasheetId || !databookId || !accessToken) {
      setLoading(false);
      return;
    }

    setLoading(true);
    fetch(`/datasheets/${datasheetId}/timeline/${databookId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        const timelineData = data.timeline || [];
        setTimeline(timelineData);

        // Extract unique users for filter
        const users = new Set(
          timelineData.map((item) => item.performed_by).filter(Boolean)
        );
        setSelectedUsers(new Set(users));
      })
      .catch((error) => {
        console.error("Error fetching timeline:", error);
        setTimeline([]);
      })
      .finally(() => setLoading(false));
  }, [datasheetId, databookId, accessToken]);

  // Filter timeline based on search and selections
  const filteredTimeline = timeline.filter((item) => {
    const matchesSearch =
      !searchTerm ||
      item.action_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.performed_by &&
        item.performed_by.toLowerCase().includes(searchTerm.toLowerCase())) ||
      Object.values(item.details || {}).some((detail) =>
        String(detail).toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesActionType = selectedActionTypes.has(item.action_type);
    const matchesUser =
      !item.performed_by || selectedUsers.has(item.performed_by);

    return matchesSearch && matchesActionType && matchesUser;
  });

  const uniqueUsers = [
    ...new Set(timeline.map((item) => item.performed_by).filter(Boolean)),
  ];

  const toggleActionType = (actionType) => {
    const newSet = new Set(selectedActionTypes);
    if (newSet.has(actionType)) {
      newSet.delete(actionType);
    } else {
      newSet.add(actionType);
    }
    setSelectedActionTypes(newSet);
  };

  const toggleUser = (user) => {
    const newSet = new Set(selectedUsers);
    if (newSet.has(user)) {
      newSet.delete(user);
    } else {
      newSet.add(user);
    }
    setSelectedUsers(newSet);
  };

  const toggleExpanded = (timestamp) => {
    const newSet = new Set(expandedItems);
    if (newSet.has(timestamp)) {
      newSet.delete(timestamp);
    } else {
      newSet.add(timestamp);
    }
    setExpandedItems(newSet);
  };

  const refreshTimeline = () => {
    setLoading(true);
    fetch(`/datasheets/${datasheetId}/timeline/${databookId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        const timelineData = data.timeline || [];
        setTimeline(timelineData);

        // Extract unique users for filter
        const users = new Set(
          timelineData.map((item) => item.performed_by).filter(Boolean)
        );
        setSelectedUsers(new Set(users));
      })
      .catch((error) => {
        console.error("Error fetching timeline:", error);
        setTimeline([]);
      })
      .finally(() => setLoading(false));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading timeline...</div>
      </div>
    );
  }

  return (
    <div className="flex h-full bg-gray-50">
      {/* Left Sidebar - Filters */}
      {showFilters && (
        <div className="w-72 bg-white border-r border-gray-200 p-6 overflow-y-auto">
          {/* Filters Header */}
          <div className="flex items-center gap-3 mb-6">
            <AdjustmentsHorizontalIcon className="w-5 h-5 text-gray-600" />
            <EverTg.Heading4 className="text-gray-900">Filters</EverTg.Heading4>
          </div>

          {/* Search */}
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-3">
              <MagnifyingGlassIcon className="w-4 h-4 text-gray-600" />
              <EverTg.Text className="text-gray-700 font-medium">
                Search
              </EverTg.Text>
            </div>
            <EverInput
              placeholder="Search changes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Action Types */}
          <div className="mb-8">
            <EverTg.Text className="text-gray-700 font-medium mb-4">
              Action Types
            </EverTg.Text>
            <div className="space-y-3">
              {Object.entries(actionTypeConfig).map(([actionType, config]) => {
                const IconComponent = config.icon;
                return (
                  <div key={actionType} className="flex items-center gap-3">
                    <EverCheckbox
                      checked={selectedActionTypes.has(actionType)}
                      onChange={() => toggleActionType(actionType)}
                    />
                    <div className={`p-1.5 rounded-md ${config.bgColor}`}>
                      <IconComponent
                        className={`w-3.5 h-3.5 ${config.color}`}
                      />
                    </div>
                    <EverTg.Caption className="text-gray-700 text-sm">
                      {config.label}
                    </EverTg.Caption>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Users */}
          {uniqueUsers.length > 0 && (
            <div>
              <EverTg.Text className="text-gray-700 font-medium mb-4">
                Users
              </EverTg.Text>
              <div className="space-y-3">
                {uniqueUsers.map((user) => (
                  <div key={user} className="flex items-center gap-3">
                    <EverCheckbox
                      checked={selectedUsers.has(user)}
                      onChange={() => toggleUser(user)}
                    />
                    <EverTg.Caption className="text-gray-700 text-sm">
                      {user}
                    </EverTg.Caption>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Action Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <EverTg.Heading4 className="text-gray-900 mb-1">
                Recent Changes
              </EverTg.Heading4>
              <EverTg.Caption className="text-gray-500">
                {filteredTimeline.length} entries
              </EverTg.Caption>
            </div>

            <div className="flex items-center gap-3">
              <EverButton
                type="ghost"
                size="small"
                onClick={refreshTimeline}
                className="flex items-center gap-2"
              >
                <ArrowPathIcon className="w-4 h-4" />
                Refresh
              </EverButton>

              <EverButton
                type="ghost"
                size="small"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <EyeSlashIcon className="w-4 h-4" />
                {showFilters ? "Hide" : "Show"} Filters
              </EverButton>
            </div>
          </div>
        </div>

        {/* Timeline Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {filteredTimeline.length === 0 ? (
            <div className="text-center py-16">
              <EverTg.Text className="text-gray-500">
                {timeline.length === 0
                  ? "No timeline data available."
                  : "No changes match your filters."}
              </EverTg.Text>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredTimeline.map((item) => {
                const config = actionTypeConfig[item.action_type];
                const IconComponent = config?.icon || CogIcon;
                const isExpanded = expandedItems.has(item.timestamp);

                return (
                  <div
                    key={item.timestamp}
                    className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start gap-4">
                      {/* Icon */}
                      <div
                        className={`p-3 rounded-xl ${
                          config?.bgColor || "bg-gray-50"
                        }`}
                      >
                        <IconComponent
                          className={`w-5 h-5 ${
                            config?.color || "text-gray-600"
                          }`}
                        />
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <EverTg.Text className="font-semibold text-gray-900 text-base">
                              {config?.label || item.action_type}
                            </EverTg.Text>
                            {config?.tag && (
                              <span
                                className={`px-3 py-1 text-xs rounded-full font-medium ${
                                  config.tag === "add"
                                    ? "bg-green-100 text-green-700"
                                    : config.tag === "edit"
                                    ? "bg-orange-100 text-orange-700"
                                    : config.tag === "delete"
                                    ? "bg-red-100 text-red-700"
                                    : "bg-gray-100 text-gray-700"
                                }`}
                              >
                                {config.tag}
                              </span>
                            )}
                          </div>
                          <EverTg.Caption className="text-gray-500 text-sm">
                            {dayjs(item.timestamp).fromNow()}
                          </EverTg.Caption>
                        </div>

                        {/* Description */}
                        {item.details?.name && (
                          <div className="mb-4">
                            <EverTg.Caption className="text-gray-600 text-sm">
                              {item.details.name}
                            </EverTg.Caption>
                          </div>
                        )}

                        {/* User */}
                        <div className="flex items-center gap-3 mb-4">
                          <EverGroupAvatar
                            avatars={[
                              {
                                name: item.performed_by || "Unknown",
                                image: null,
                              },
                            ]}
                            size="small"
                          />
                          <EverTg.Caption className="text-gray-600 text-sm">
                            {item.performed_by || "Unknown"}
                          </EverTg.Caption>
                        </div>

                        {/* Show Details Button */}
                        {Object.keys(item.details || {}).length > 0 && (
                          <button
                            onClick={() => toggleExpanded(item.timestamp)}
                            className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium"
                          >
                            {isExpanded ? (
                              <>
                                <ChevronUpIcon className="w-4 h-4" />
                                Hide details
                              </>
                            ) : (
                              <>
                                <ChevronDownIcon className="w-4 h-4" />
                                Show details
                              </>
                            )}
                          </button>
                        )}

                        {/* Expanded Details */}
                        {isExpanded && (
                          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                            {Object.entries(item.details || {}).map(
                              ([key, value]) => (
                                <div key={key} className="mb-3 last:mb-0">
                                  <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                    {key}:
                                  </EverTg.Caption>
                                  <EverTg.Caption className="text-gray-700 ml-2 text-sm">
                                    {typeof value === "object"
                                      ? JSON.stringify(value, null, 2)
                                      : String(value)}
                                  </EverTg.Caption>
                                </div>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SheetTimeline;
