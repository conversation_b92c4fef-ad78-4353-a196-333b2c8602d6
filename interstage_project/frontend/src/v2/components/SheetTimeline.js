import React, { useEffect, useState } from "react";
import { EverList } from "./EverList";
import { EverListItem } from "./EverListItem";
import { EverGroupAvatar } from "./EverGroupAvatar";
import { EverTypography } from "./EverTypography";
import { EverTooltip } from "./EverTooltip";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(relativeTime);

const actionTypeLabels = {
  sheet_created: "Sheet Created",
  sheet_archived: "Sheet Archived",
  sheet_generated: "Sheet Generated",
  transformation_added: "Transformation Added",
  transformation_edited: "Transformation Edited",
  variable_added: "Variable Added",
  variable_edited: "Variable Edited",
  variable_selected: "Variable Selected",
  variable_deselected: "Variable Deselected",
  adjustment_added: "Adjustment Added",
  permission_changed: "Permission Changed",
  // ...add more as needed
};

const SheetTimeline = ({ datasheetId, databookId }) => {
  const [timeline, setTimeline] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    fetch(`/api/datasheets/${datasheetId}/timeline/${databookId}`)
      .then((res) => res.json())
      .then((data) => setTimeline(data.timeline || []))
      .finally(() => setLoading(false));
  }, [datasheetId, databookId]);

  if (loading) return <div>Loading...</div>;

  return (
    <EverList>
      {timeline.map((item) => (
        <EverListItem key={item.timestamp}>
          <EverGroupAvatar>
            {item.performed_by ? item.performed_by[0].toUpperCase() : "?"}
          </EverGroupAvatar>
          <div>
            <EverTypography variant="h6">
              {actionTypeLabels[item.action_type] || item.action_type}
            </EverTypography>
            <EverTypography variant="body2">{item.performed_by}</EverTypography>
            <EverTooltip
              title={dayjs(item.timestamp).format("YYYY-MM-DD HH:mm")}
            >
              <EverTypography variant="caption">
                {dayjs(item.timestamp).fromNow()}
              </EverTypography>
            </EverTooltip>
            <div>
              {Object.entries(item.details || {}).map(([k, v]) => (
                <div key={k}>
                  <b>{k}:</b>{" "}
                  {typeof v === "object" ? JSON.stringify(v) : String(v)}
                </div>
              ))}
            </div>
          </div>
        </EverListItem>
      ))}
    </EverList>
  );
};

export default SheetTimeline;
