import logging
import traceback

from django.http import FileResponse
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.response import Response

from commission_engine.custom_exceptions.etl_exceptions import ETLConcurrencyException
from commission_engine.utils.general_data import RbacPermissions
from everstage_ddd.stormbreaker.stormbreaker_exception import (
    UserDoesNotHavePermissionError,
)
from interstage_project.auth_utils import requires_scope
from interstage_project.utils import handle_datasheet_version

from .base_view import DatasheetBaseView
from .data_models import (
    CreateAdjustmentRequest,
    DatasheetCloneRequest,
    DatasheetCloneResponse,
    DatasheetDataRequest,
    DatasheetGenerateRequest,
    DatasheetGenerateResponse,
    DatasheetGraphRequest,
    DatasheetSyncStatusRequest,
    DatasheetTagModel,
    DatasheetValidateRequest,
    ExportDatasheetRequest,
    GetAdjustmentsRequest,
    RevertAdjustmentRequest,
    UpdateAdjustmentRequest,
)
from .enums import DatasheetGroupedBy
from .services.datasheet_adjustment import (
    datasheet_adjustment_create,
    fetch_datasheet_adjustment_data,
    revert_datasheet_adjustment,
    update_datasheet_adjustment,
)
from .services.datasheet_data import datasheet_data_fetch
from .services.datasheet_pin_service import (
    create_datasheet_pin,
    delete_datasheet_pin,
    get_all_pinned_datasheets_for_client,
)
from .services.datasheet_service import (
    clone_datasheet_wrapper,
    datasheet_source_options,
    datasheet_update,
    fetch_all_ds_filter_operators,
    get_datasheet_details_for_canvas,
    get_datasheet_details_for_edit,
    get_datasheet_graph_data,
    get_datasheet_sync_details,
    get_datasheets_grouped_by_archived_databooks,
    get_datasheets_grouped_by_commission_plans,
    get_datasheets_grouped_by_databook_id,
    get_export_datasheet_data_as_csv,
    get_force_skipped_datasheet_ids,
    get_output_columns_for_transformation,
    get_sheet_timeline,
    refresh_datasheet,
    validate_datasheet,
)
from .services.datasheet_tag_map import (
    datasheet_tag_map_create,
    datasheet_tag_map_delete,
)
from .services.datasheet_tags import datasheet_tag_options_for_datasheet
from .services.datasheet_variables import datasheet_variables_fetch
from .services.datasheet_view import (
    datasheet_view_clone,
    datasheet_view_create,
    datasheet_view_data_fetch,
    datasheet_view_delete,
    datasheet_view_details,
    datasheet_view_update,
)
from .services.datasheet_view_filter import datasheet_view_filter_create
from .services.datasheet_view_pivot import datasheet_view_pivot_create
from .services.export_datsheet_services import datasheet_exportv2_iniator
from .utils.datasheet_utils import handle_datasheet_exception

logger = logging.getLogger(__name__)


class GetDatasheetsViewBy(DatasheetBaseView):
    @handle_datasheet_exception
    @handle_datasheet_version("v2")
    def get(self, request):
        """
        Get Datasheets Grouped By Databook, Commission Plan.

        Retrieves datasheets grouped by a specified criterion such as Databook or Commission Plan.
        The grouping criterion is specified via the 'criteria' query parameter.

        Example request:
            GET /api/datasheets/view-by?criteria=databook
            Accept: application/json

        Parameters:
            - criteria (str): Criterion for grouping datasheets. Defaults to 'databook'. Supported values are 'databook', 'commission_plan'.

        Responses:
            - 200 OK: Successfully retrieved grouped datasheets.
              Example response:
              {
                  "databooks": [
                      {
                          "id": "uuid-databook-1",
                          "name": "Databook 1",
                          "datasheets": [
                              {
                                  "name": "Datasheet 1",
                                  "datasheetId": "uuid-datasheet-1",
                                  "order": 1
                              },
                              {
                                  "name": "Datasheet 2",
                                  "datasheetId": "uuid-datasheet-2",
                                  "order": 2
                              }
                          ]
                      }
                  ]
              }

            - 400 Bad Request: The 'grouped_by' parameter is invalid or missing.
              Example error response:
              {
                  "error": "Invalid 'grouped_by' parameter.",
                  "supported_values": ["databook", "commission_plan"]
              }
        """
        client_id = request.client_id

        grouped_by_value = request.query_params.get(
            "criteria", DatasheetGroupedBy.DATABOOK.value
        )

        try:
            grouped_by = DatasheetGroupedBy(grouped_by_value)
        except ValueError:
            # Handle the case where the value is not a valid option for the enum
            # and return the supported 'criteria' values in the error response.
            supported_values = [e.value for e in DatasheetGroupedBy]
            return Response(
                {
                    "error": "Invalid 'criteria' parameter.",
                    "supported_values": supported_values,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if grouped_by == DatasheetGroupedBy.COMMISSION_PLAN:
            datasheets = get_datasheets_grouped_by_commission_plans(
                client_id=client_id, login_user_id=request.user.username
            )
        elif grouped_by == DatasheetGroupedBy.ARCHIVED_DATABOOK:
            datasheets = get_datasheets_grouped_by_archived_databooks(
                client_id=client_id, user_id=request.user.username
            )
        else:
            datasheets = get_datasheets_grouped_by_databook_id(
                client_id=client_id, user_id=request.user.username
            )

        return Response(datasheets.model_dump(), status=status.HTTP_200_OK)


class GetPinnedDatasheetsView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def get(self, request):
        client_id = request.client_id
        datasheets = get_all_pinned_datasheets_for_client(client_id)
        return Response(
            datasheets.model_dump(),
            status=status.HTTP_200_OK,
        )


class AddDatasheetPinView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request, datasheet_id):
        client_id = request.client_id
        current_user = request.user.username
        create_datasheet_pin(client_id, datasheet_id, current_user)
        return Response(
            {"message": "Datasheet pinned successfully"},
            status=status.HTTP_200_OK,
        )


class RemoveDatasheetPinView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request, datasheet_id):
        client_id = request.client_id
        current_user = request.user.username
        delete_datasheet_pin(client_id, datasheet_id, current_user)
        return Response(
            {"message": "Datasheet unpinned successfully"},
            status=status.HTTP_200_OK,
        )


class AddTagToDatasheet(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request):
        client_id = request.client_id
        datasheet_id = request.data["datasheet_id"]
        tag_details = DatasheetTagModel(**request.data, client_id=client_id)
        record = datasheet_tag_map_create(
            datasheet_id=datasheet_id,
            tag_details=tag_details,
        )
        return Response(
            {"data": record.model_dump(by_alias=True)},
            status=status.HTTP_201_CREATED,
        )


class RemoveTagOnDatasheet(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request):
        client_id = request.client_id
        datasheet_tag_map_delete(
            client_id=client_id,
            datasheet_id=request.data["datasheet_id"],
            tag_id=request.data["tag_id"],
        )
        return Response(
            {"message": "Tag removed from datasheet Successfully"},
            status=status.HTTP_200_OK,
        )


class ListTagOptionsForDatasheet(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def get(self, request, datasheet_id):
        client_id = request.client_id
        records = datasheet_tag_options_for_datasheet(
            client_id=client_id, datasheet_id=datasheet_id
        )
        return Response(
            {"data": [record.model_dump(by_alias=True) for record in records]},
            status=status.HTTP_200_OK,
        )


class DatasheetDataListView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request, datasheet_id):
        client_id = request.client_id
        databook_id = request.data.pop("databook_id", None)
        request_model = DatasheetDataRequest(
            **request.data,
            client_id=client_id,
            datasheet_id=datasheet_id,
            databook_id=databook_id,
            logged_in_user_email=str(request.user),
        )
        result = datasheet_data_fetch(
            request_model=request_model,
        )
        return Response(
            result.model_dump(by_alias=True),
            status=status.HTTP_200_OK,
        )


class DatasheetRecord(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    @handle_datasheet_version("v2")
    def patch(self, request, datasheet_id):
        client_id = request.client_id
        datasheet_update(
            client_id=client_id,
            datasheet_id=datasheet_id,
            data=request.data,
        )
        return Response(
            data={
                "message": "Datasheet updated successfully",
            },
            status=status.HTTP_200_OK,
        )


class DatasheetRecordForCanvas(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def get(self, request, datasheet_id):
        try:
            client_id = request.client_id
            logged_in_user = str(request.user)
            datasheet = get_datasheet_details_for_canvas(
                client_id=client_id,
                datasheet_id=datasheet_id,
                logged_in_user=logged_in_user,
            )
            return Response(
                datasheet.model_dump(
                    by_alias=True,
                    exclude={"variables": {"root": {"source_name_history": True}}},
                ),
                status=status.HTTP_200_OK,
            )
        except UserDoesNotHavePermissionError:
            logger.exception(
                "User does not have permission to view datasheet %s",
                datasheet_id,
            )
            return Response(
                {
                    "error": {
                        "code": "FORBIDDEN",
                        "message": "You don't have permission to view the contents of this sheet",
                    }
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as ex:
            logger.exception(
                "Error while getting datasheet %s", datasheet_id, exc_info=ex
            )
            return Response(
                {"message": str(ex)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DatasheetRecordForEdit(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def get(self, request, datasheet_id):
        try:
            client_id = request.client_id
            datasheet = get_datasheet_details_for_edit(
                client_id=client_id,
                datasheet_id=datasheet_id,
            )
            return Response(
                datasheet.model_dump(by_alias=True),
                status=status.HTTP_200_OK,
            )
        except UserDoesNotHavePermissionError:
            logger.exception(
                "User does not have permission to view datasheet %s",
                datasheet_id,
            )
            return Response(
                {
                    "error": {
                        "code": "FORBIDDEN",
                        "message": "You don't have permission to view the contents of this sheet",
                    }
                },
                status=status.HTTP_403_FORBIDDEN,
            )
        except Exception as ex:
            logger.exception(
                "Error while getting datasheet %s", datasheet_id, exc_info=ex
            )
            return Response(
                {"message": str(ex)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DatasheetViewCreate(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id):
        client_id = request.client_id
        record = datasheet_view_create(
            view_details=request.data,
            client_id=client_id,
            datasheet_id=datasheet_id,
            current_user=str(request.user),
        )
        return Response(
            data={
                "view_id": record.view_id,
                "message": "Datasheet View created successfully",
            },
            status=status.HTTP_200_OK,
        )


class DatasheetViewClone(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id, view_id):
        try:
            client_id = request.client_id
            cloned_view = datasheet_view_clone(
                client_id=client_id,
                view_id=view_id,
                datasheet_id=datasheet_id,
                current_user=str(request.user),
            )
            return Response(
                data={
                    "message": "Datasheet view cloned successfully.",
                    "view": cloned_view.model_dump(by_alias=True),
                },
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while fetching details for view_id %s",
                view_id,
                exc_info=ex,
            )


class DatasheetViewRecord(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    def get(self, request, datasheet_id, view_id):
        try:
            client_id = request.client_id
            record = datasheet_view_details(
                client_id=client_id,
                view_id=view_id,
                datasheet_id=datasheet_id,
                logged_in_user=str(request.user),
            )
            return Response(
                record.model_dump(by_alias=True),
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while fetching details for view_id %s",
                view_id,
                exc_info=ex,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def patch(self, request, datasheet_id, view_id):
        try:
            client_id = request.client_id
            datasheet_view_update(
                client_id=client_id,
                view_id=view_id,
                datasheet_id=datasheet_id,
                data=request.data,
                current_user=str(request.user),
            )
            return Response(
                {"message": "Datasheet View updated successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while updating view for view_id %s",
                view_id,
                exc_info=ex,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def delete(self, request, datasheet_id, view_id):
        try:
            client_id = request.client_id
            datasheet_view_delete(
                client_id=client_id,
                view_id=view_id,
                datasheet_id=datasheet_id,
                current_user=str(request.user),
            )
            return Response(
                {"message": "Datasheet View deleted successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while deleting view for view_id %s",
                view_id,
                exc_info=ex,
            )


class DatasheetViewFilterCreate(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id, view_id):
        try:
            client_id = request.client_id
            datasheet_view_filter_create(
                filter_details=request.data,
                client_id=client_id,
                view_id=view_id,
                datasheet_id=datasheet_id,
                current_user=str(request.user),
            )
            return Response(
                data={"message": "Datasheet Filter created successfully"},
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while creating filter for view_id %s",
                view_id,
                exc_info=ex,
            )


class DatasheetViewPivotCreate(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request, datasheet_id, view_id):
        client_id = request.client_id
        datasheet_view_pivot_create(
            pivot_details=request.data,
            client_id=client_id,
            view_id=view_id,
            datasheet_id=datasheet_id,
            current_user=str(request.user),
        )
        return Response(
            data={"message": "Datasheet Pivot created successfully"},
            status=status.HTTP_200_OK,
        )


class DatasheetViewData(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    def post(self, request, datasheet_id, view_id):
        try:
            client_id = request.client_id
            result = datasheet_view_data_fetch(
                client_id=client_id,
                datasheet_id=datasheet_id,
                view_id=view_id,
                logged_in_user_email=str(request.user),
                **request.data,
            )
            return Response(
                result.model_dump(by_alias=True),
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while getting data for datasheet %s",
                datasheet_id,
                exc_info=ex,
            )


class DatasheetSourceOptions(DatasheetBaseView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.VIEW_DATABOOK.value,
                RbacPermissions.MANAGE_DATASETTINGS.value,
            ]
        ),
        name="dispatch",
    )
    @handle_datasheet_exception
    def get(self, request):
        client_id = request.client_id
        datasheet_id = request.query_params.get("datasheet_id", None)
        datasheet_source_options_model = datasheet_source_options(
            client_id=client_id, datasheet_id=datasheet_id
        )
        return Response(
            datasheet_source_options_model.model_dump(),
            status=status.HTTP_200_OK,
        )


class DatasheetVariables(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def get(self, request, datasheet_id):
        client_id = request.client_id
        only_selected = request.query_params.get("only-selected", False)
        variables = datasheet_variables_fetch(
            client_id=client_id, datasheet_id=datasheet_id, only_selected=only_selected
        )
        return Response(
            variables.model_dump(by_alias=True),
            status=status.HTTP_200_OK,
        )


class DatasheetAdjustmentsUpdate(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value),
        name="dispatch",
    )
    @handle_datasheet_version("v2")
    def patch(self, request, datasheet_id, adjustment_id):
        try:
            client_id = request.client_id
            request.data["databook_id"] = request.data.get("databook_id")
            request.data["adjustment_id"] = adjustment_id
            ds_adjustment_model = UpdateAdjustmentRequest(
                **request.data, client_id=client_id, datasheet_id=datasheet_id
            )
            result = update_datasheet_adjustment(
                client_id=client_id,
                datasheet_id=datasheet_id,
                logged_in_user=str(request.user),
                adjustment_payload=ds_adjustment_model,
                audit=request.audit,
            )
            if result.status == "SUCCESS":
                return Response(
                    result.model_dump(by_alias=True),
                    status=status.HTTP_200_OK,
                )

            return Response(
                result.model_dump(by_alias=True),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as ex:
            logger.exception(
                "Error while reverting an adjustment for datasheet",
                exc_info=ex,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value),
        name="dispatch",
    )
    @handle_datasheet_version("v2")
    def delete(self, request, datasheet_id, adjustment_id):
        try:
            client_id = request.client_id
            request.data["databook_id"] = request.data.get("databook_id")
            request.data["adjustment_id"] = adjustment_id
            ds_adjustment_model = RevertAdjustmentRequest(
                **request.data, client_id=client_id, datasheet_id=datasheet_id
            )
            result = revert_datasheet_adjustment(
                client_id=client_id,
                datasheet_id=datasheet_id,
                logged_in_user=str(request.user),
                adjustment_payload=ds_adjustment_model,
                audit=request.audit,
            )
            if result.status == "SUCCESS":
                return Response(
                    result.model_dump(by_alias=True),
                    status=status.HTTP_200_OK,
                )

            return Response(
                result.model_dump(by_alias=True),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as ex:
            logger.exception(
                "Error while reverting an adjustment for datasheet",
                exc_info=ex,
            )


class DatasheetAdjustments(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value),
        name="dispatch",
    )
    def get(self, request, datasheet_id):
        try:
            client_id = request.client_id
            adjustments_req = GetAdjustmentsRequest(
                **{
                    "client_id": client_id,
                    "datasheet_id": datasheet_id,
                    "databook_id": None,
                }
            )

            databook_id = adjustments_req.databook_id

            adjustments = fetch_datasheet_adjustment_data(
                client_id=client_id,
                datasheet_id=datasheet_id,
                logged_in_user=str(request.user),
                databook_id=databook_id,
            )

            if adjustments.status == "SUCCESS":
                return Response(
                    adjustments.model_dump(by_alias=True),
                    status=status.HTTP_200_OK,
                )

            return Response(
                adjustments.model_dump(by_alias=True),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as ex:
            logger.exception(
                "Error while fetching adjustments for datasheet %s",
                datasheet_id,
                exc_info=ex,
            )

    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value),
        name="dispatch",
    )
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id):
        try:
            client_id = request.client_id
            request.data["databook_id"] = request.data.get("databook_id")
            ds_adjustment_model = CreateAdjustmentRequest(
                **request.data,
                datasheet_id=datasheet_id,
                client_id=client_id,
            )
            result = datasheet_adjustment_create(
                logged_in_user=str(request.user),
                adjustment_payload=ds_adjustment_model,
                audit=request.audit,
            )
            if result.status == "SUCCESS":
                return Response(
                    result.model_dump(by_alias=True),
                    status=status.HTTP_200_OK,
                )

            return Response(
                result.model_dump(by_alias=True),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as ex:
            logger.exception(
                "Error while creating an adjustment for datasheet",
                exc_info=ex,
            )


class DatasheetValidation(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id):
        client_id = request.client_id
        request_model = DatasheetValidateRequest(
            **request.data,
            datasheet_id=datasheet_id,
            client_id=client_id,
            databook_id=None,
        )
        result = validate_datasheet(
            request_model=request_model,
        )
        return Response(
            result.model_dump(by_alias=True),
            status=status.HTTP_200_OK,
        )


class DatasheetSyncStatus(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    def get(self, request, datasheet_id):
        try:
            client_id = request.client_id
            datasheet_sync_status_request = DatasheetSyncStatusRequest(
                **{
                    "client_id": client_id,
                    "datasheet_id": datasheet_id,
                    "databook_id": None,
                }
            )

            databook_id = datasheet_sync_status_request.databook_id

            datasheet_sync_response = get_datasheet_sync_details(
                client_id=client_id, datasheet_id=datasheet_id, databook_id=databook_id
            )

            return Response(
                datasheet_sync_response.model_dump(by_alias=True),
                status=status.HTTP_200_OK,
            )
        except Exception as ex:
            logger.exception(
                "Error while fetching sync status for datasheet %s",
                datasheet_id,
                exc_info=ex,
            )


class DatasheetExportView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.EXPORT_DATASHEET.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id):
        try:
            client_id = request.client_id
            export_datasheet_request = ExportDatasheetRequest(
                **request.data,
                client_id=client_id,
                datasheet_id=datasheet_id,
                databook_id=None,
            )

            datasheet_as_csv, csv_file_name = get_export_datasheet_data_as_csv(
                export_datasheet_request,
                str(request.user),
                str(request.audit["updated_by"]),
            )

            return FileResponse(
                datasheet_as_csv, as_attachment=True, filename=csv_file_name
            )
        except Exception as ex:
            logger.exception(
                "Error while exporting datasheet %s",
                datasheet_id,
                exc_info=ex,
            )


class GenerateDatasheetView(DatasheetBaseView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_DATABOOK.value,
                RbacPermissions.MANAGE_DATASHEETADJUSTMENTS.value,
            ]
        ),
        name="dispatch",
    )
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id):
        try:
            client_id = request.client_id
            request.data["databook_id"] = request.data.get("databook_id")
            audit = request.audit
            datasheet_generate_model = DatasheetGenerateRequest(
                **request.data,
                client_id=client_id,
                datasheet_id=datasheet_id,
            )

            response_model = refresh_datasheet(datasheet_generate_model, audit)

            # task created successfully
            if isinstance(response_model, DatasheetGenerateResponse):
                return Response(
                    response_model.task_info.model_dump(by_alias=True),
                    status=response_model.status_code,
                )

            # task creation failed
            return Response(
                data={
                    "error": {
                        "code": response_model.code,
                        "message": response_model.message,
                    }
                },
                status=response_model.status,
            )

        except ETLConcurrencyException as e:
            # return a respose with an error field
            return Response(
                {"error": {"message": str(e)}},
                status=status.HTTP_409_CONFLICT,
            )

        except Exception:  # noqa: BLE001
            logger.info("DATABOOK GEN DATA EXCEP.. {}".format(traceback.print_exc()))
            return Response(
                "Datasheet generation failed", status=status.HTTP_400_BAD_REQUEST
            )


class DSFilterOperatorView(DatasheetBaseView):
    @method_decorator(
        requires_scope(
            [
                RbacPermissions.MANAGE_DATASETTINGS.value,
                RbacPermissions.MANAGE_CONTRACTS.value,
                RbacPermissions.MANAGE_USERGROUPS.value,
                RbacPermissions.VIEW_DATABOOK.value,
                RbacPermissions.VIEW_DASHBOARD.value,
            ]
        ),
        name="dispatch",
    )
    def get(self, _request):
        try:
            result = fetch_all_ds_filter_operators()
            return Response(
                result.model_dump(),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.exception("Error while getting ds filter operators")
            return Response(
                str(e),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class DatasheetGraphView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def post(self, request):
        try:
            client_id = request.client_id
            datasheet_graphq_model = DatasheetGraphRequest(
                **request.data,
                client_id=client_id,
            )

            datasheet_graph = get_datasheet_graph_data(
                datasheet_graphq_model, logged_in_user=str(request.user)
            )

            return Response(datasheet_graph, status=status.HTTP_200_OK)
        except Exception as ex:
            logger.exception(
                "Error in fetching databook graph for databook_id %s",
                request.data.databook_id,
                exc_info=ex,
            )
            return Response({"status": "error"}, status=status.HTTP_400_BAD_REQUEST)


class DatasheetClone(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.MANAGE_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    @handle_datasheet_exception
    def post(self, request, datasheet_id):
        client_id = request.client_id
        logged_in_user = request.user
        request_model = DatasheetCloneRequest(
            **request.data,
            datasheet_id=datasheet_id,
            client_id=client_id,
        )
        result = clone_datasheet_wrapper(
            request_model=request_model,
            logged_in_user=logged_in_user,
            audit=request.audit,
        )
        if isinstance(result, DatasheetCloneResponse):
            return Response(
                {
                    "message": "Datasheet cloned successfully",
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {"message": "Datasheet cloning failed"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


class GetOutputColumnsForTransformation(DatasheetBaseView):

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def post(self, request, datasheet_id):
        result = get_output_columns_for_transformation(
            client_id=request.client_id,
            datasheet_id=datasheet_id,
            data=request.data,
        )
        return Response(result, status=status.HTTP_200_OK)


class DatasheetExportViewV2(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.EXPORT_DATASHEET.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    def post(self, request, datasheet_id):
        try:
            client_id = request.client_id
            export_datasheet_request = ExportDatasheetRequest(
                **request.data,
                client_id=client_id,
                datasheet_id=datasheet_id,
                databook_id=None,
            )

            datasheet_exportv2_iniator(
                export_datasheet_request,
                str(request.user),
                str(request.audit["updated_by"]),
                request.data.get("export_type", "csv"),
            )

            return Response({"message": "Success"}, status=status.HTTP_202_ACCEPTED)
        except Exception as ex:
            logger.exception(
                "Error while exporting datasheet %s",
                datasheet_id,
                exc_info=ex,
            )


class ForceSkippedDatasheetIDsView(DatasheetBaseView):
    """
    API endpoint to get all force skipped (archived) datasheet IDs for the client.
    """

    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_version("v2")
    @handle_datasheet_exception
    def get(self, request):
        client_id = request.client_id
        skipped_ids = get_force_skipped_datasheet_ids(client_id)
        return Response(
            {"skipped_datasheet_ids": skipped_ids}, status=status.HTTP_200_OK
        )


class DatasheetTimelineView(DatasheetBaseView):
    @method_decorator(
        requires_scope(RbacPermissions.VIEW_DATABOOK.value), name="dispatch"
    )
    @handle_datasheet_exception
    def get(self, request, datasheet_id, databook_id):
        """
        Returns the edit history/timeline for a datasheet in chronological order.
        """
        client_id = request.client_id
        timeline = get_sheet_timeline(sheet_id=datasheet_id, databook_id=databook_id, client_id=client_id)
        return Response({"timeline": timeline}, status=status.HTTP_200_OK)
